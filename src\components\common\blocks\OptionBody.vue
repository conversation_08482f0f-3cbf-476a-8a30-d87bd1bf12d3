<template>
  <div class="q-ml-md">
    <!-- รายการตัวเลือก (Radio Input หน้า Text Field) -->
    <div
      v-for="(_option, index) in store.radioOptions"
      :key="index"
      class="row items-center q-mb-sm draggable-row"
      @dragover.prevent
      @drop="store.drop(index, $event)"
      :style="{ transition: 'all 0.3s ease', opacity: store.draggedIndex === index ? 0.5 : 1 }"
    >
      <q-btn
        flat
        round
        icon="drag_indicator"
        color="grey"
        @mousedown="store.startDrag(index)"
        draggable="true"
        @dragstart="store.handleDragStart($event)"
        @dragend="store.endDrag"
        @mouseover="store.hoverRow(index)"
        style="cursor: move"
      />
      <q-radio
        v-model="store.checkboxOptions"
        :val="store.radioOptions[index]?.value"
        color="primary"
        disable
        class="q-mr-sm"
      />
      <div class="row items-center">
        <q-input
          v-model="store.radioOptions[index]!.optionText"
          :placeholder="store.radioOptions[index]!.placeholder || `ตัวเลือกที่ ${index + 1}`"
          dense
          @update:model-value="() => handleOptionTextChange(index)"
          @blur="handleOptionTextBlur(index)"
          class="q-mr-sm"
          :key="`option-${index}-${store.radioOptions.length}`"
        />
        <q-input
          v-if="props.type === 'quiz'"
          v-model.number="store.radioOptions[index]!.score"
          placeholder="คะแนน"
          type="number"
          dense
          style="max-width: 80px"
          @input="handleOptionScoreChange(index)"
          @blur="handleOptionScoreBlur(index, $event)"
        />
      </div>
      <q-btn flat round icon="image" color="grey" class="q-ml-sm" />
      <q-btn
        v-if="store.radioOptions.length > 1"
        flat
        round
        icon="close"
        @click="handleDeleteOption(index)"
        :disable="store.radioOptions.length <= 1"
        class="q-ml-sm"
      />
    </div>

    <!-- ปุ่มเพิ่มช้อย -->
    <div class="row items-center q-mt-sm">
      <q-btn
        flat
        color="secondary"
        label="เพิ่มตัวเลือก"
        icon="add"
        @click="handleAddOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
      <q-btn
        flat
        color="secondary"
        label="เพิ่ม 'อื่นๆ'"
        icon="add"
        @click="handleAddOtherOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref, nextTick } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock, Option } from 'src/types/models';
import { OptionService, type CreateOptionData } from 'src/services/asm/optionService';

// Define interface for option update data
interface OptionUpdateData {
  index: number;
  option: Option;
}

// Inject the store from the parent component
const store = inject<ItemBlockStore>('blockStore');

// Inject auto-save functions from parent ItemBlockComponent
const autoSave = inject<{
  triggerOptionAutoSave: (
    optionId: number,
    field: 'optionText' | 'value',
    value: string | number,
  ) => void;
  isSaving: { value: boolean };
}>('autoSave');

// Make sure the store is available
if (!store) {
  console.error('ItemBlockStore not provided to ChoiceAns component');
  throw new Error('ItemBlockStore not provided to ChoiceAns component');
}

// Get the itemBlock prop to access actual option IDs
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
}>();

// Define emits for updating parent component
const emit = defineEmits<{
  'update:options': [options: Option[]];
  'option:created': [option: Option];
  'option:updated': [optionId: number, data: OptionUpdateData];
  'option:deleted': [data: { deletedOptionId: number; deletedIndex: number; itemBlockId: number }];
}>();

// Initialize option service
const optionService = new OptionService();

// Loading state for option creation
const isCreatingOption = ref(false);

// Debug function to compare frontend and backend state
const debugOptionState = () => {
  console.log('🔍 Current Option State Debug:', {
    itemBlockId: props.itemBlock.id,
    frontendStore: {
      radioOptionsCount: store.radioOptions.length,
      radioOptions: store.radioOptions.map((opt, idx) => ({
        index: idx,
        optionText: opt.optionText,
        score: opt.score,
        placeholder: opt.placeholder,
        value: opt.value,
      })),
    },
    backendOptions: {
      optionsCount: props.itemBlock.options?.length || 0,
      options:
        props.itemBlock.options?.map((opt, idx) => ({
          index: idx,
          id: opt.id,
          optionText: opt.optionText,
          value: opt.value,
          sequence: opt.sequence,
        })) || [],
    },
    timestamp: new Date().toISOString(),
  });
};

// Expose debug function to window for console access
if (typeof window !== 'undefined') {
  (window as any).debugOptionState = debugOptionState;
}

// Handler for option text changes (real-time updates without saving)
const handleOptionTextChange = (index: number) => {
  // Update the store immediately for UI responsiveness
  store.updateOption(index);
  // No auto-save here - save only on blur
};

// Handler for option text blur (save on blur)
const handleOptionTextBlur = async (index: number) => {
  const actualOption = props.itemBlock.options?.[index];
  const optionText = store.radioOptions[index]?.optionText || '';

  // If option doesn't have an ID yet, it needs to be created first
  if (actualOption && !actualOption.id) {
    // Only create if user has entered some text
    if (optionText.trim()) {
      try {
        const optionData: CreateOptionData = {
          optionText: optionText,
          itemBlockId: props.itemBlock.id,
          value: store.radioOptions[index]?.score || 0,
        };

        const createdOption = await optionService.createOption(optionData);

        // Emit option update to parent component
        emit('option:updated', createdOption.id, {
          index,
          option: createdOption,
        });
      } catch (error) {
        console.error('❌ Failed to create option on blur:', error);
      }
    }
  } else if (actualOption && actualOption.id && autoSave) {
    // Option exists, trigger save via parent component
    autoSave.triggerOptionAutoSave(actualOption.id, 'optionText', optionText);
  }
};

// Handler for option score changes (real-time updates without saving)
const handleOptionScoreChange = (index: number) => {
  // Update the store for real-time UI updates
  store.updateOption(index);
  // No auto-save here - save only on blur
};

// Handler for option score blur (save on blur)
const handleOptionScoreBlur = async (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = Number(target.value) || 0;
  const actualOption = props.itemBlock.options?.[index];

  if (actualOption && actualOption.id && autoSave) {
    // For score changes, we need to update the value field
    autoSave.triggerOptionAutoSave(actualOption.id, 'value', newValue);
  } else if (actualOption && actualOption.id) {
    // Fallback: use optionService directly if auto-save is not available
    try {
      await optionService.updateOption(actualOption.id, {
        optionText: actualOption.optionText,
        itemBlockId: props.itemBlock.id,
        value: newValue,
      });
    } catch (error) {
      console.error('Failed to update option score:', error);
    }
  }
};

// Handler for deleting an option
const handleDeleteOption = async (index: number) => {
  // CRITICAL FIX: Find option by ID instead of relying on index
  // The index might be stale after previous deletions
  const actualOption = props.itemBlock.options?.[index];

  // Double-check: if the option at this index doesn't exist, try to find it by matching frontend store
  let optionToDelete = actualOption;

  try {
    if (!optionToDelete && store.radioOptions[index]) {
      // Frontend store has an option at this index, but backend doesn't
      // This indicates a sync issue - try to find the option by other means
      const frontendOption = store.radioOptions[index];
      console.warn('⚠️ Index mismatch detected, attempting to find option by content:', {
        index,
        frontendOption: frontendOption.optionText,
        backendOptions: props.itemBlock.options?.map((opt) => ({
          id: opt.id,
          text: opt.optionText,
        })),
      });

      // Try to find matching option by text content (fallback)
      optionToDelete = props.itemBlock.options?.find(
        (opt) => opt.optionText === frontendOption.optionText,
      );
    }

    console.log(`🗑️ OptionBody: Attempting to delete option at index ${index}:`, {
      index,
      originalOptionId: actualOption?.id,
      resolvedOptionId: optionToDelete?.id,
      optionText: optionToDelete?.optionText,
      itemBlockId: props.itemBlock.id,
      totalOptionsInBackend: props.itemBlock.options?.length || 0,
      totalOptionsInStore: store.radioOptions.length,
      indexMismatchDetected: !actualOption && !!optionToDelete,
      backendOptionsDetails:
        props.itemBlock.options?.map((opt, idx) => ({
          index: idx,
          id: opt.id,
          optionText: opt.optionText,
          sequence: opt.sequence,
        })) || [],
      frontendStoreDetails: store.radioOptions.map((opt, idx) => ({
        index: idx,
        optionText: opt.optionText,
        score: opt.score,
      })),
    });

    // If option has an ID, delete it from backend first
    if (optionToDelete && optionToDelete.id) {
      console.log(`🗑️ Calling backend delete for option ID: ${optionToDelete.id}`);
      await optionService.removeOption(optionToDelete.id);
      console.log(`✅ Backend delete successful for option ID: ${optionToDelete.id}`);

      // CRITICAL: Update frontend store IMMEDIATELY to prevent UI issues
      console.log(`🗑️ Removing option from local store at index: ${index}`);
      store.removeOption(index);
      console.log(`✅ Option removed from local store`);

      // THEN emit deletion to parent to update backend options array
      emit('option:deleted', {
        deletedOptionId: optionToDelete.id,
        deletedIndex: index,
        itemBlockId: props.itemBlock.id,
      });

      console.log('🔄 After deletion, current state:', {
        backendOptionsCount: props.itemBlock.options?.length || 0,
        frontendStoreCount: store.radioOptions.length,
        deletedOptionId: optionToDelete.id,
      });
    } else {
      console.warn('⚠️ No option ID found - skipping backend delete:', {
        originalOption: actualOption,
        resolvedOption: optionToDelete,
        hasOriginalId: actualOption?.id,
        hasResolvedId: optionToDelete?.id,
        index,
      });
    }
  } catch (error) {
    console.error('❌ Failed to delete option:', error);
    console.error('❌ Delete operation context:', {
      index,
      itemBlockId: props.itemBlock.id,
      originalOptionId: props.itemBlock.options?.[index]?.id,
      resolvedOptionId: optionToDelete?.id,
      backendOptionsCount: props.itemBlock.options?.length || 0,
      frontendStoreCount: store.radioOptions.length,
      timestamp: new Date().toISOString(),
    });
  }
};

// Handler for adding new option
const handleAddOption = async () => {
  if (isCreatingOption.value) return;

  const buttonClickTime = new Date().toISOString();
  const clickTimestamp = performance.now();
  console.log(
    `🖱️ [${buttonClickTime}] Add Option button clicked for itemBlockId: ${props.itemBlock.id}`,
  );

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOption();

    // Force reactivity update to ensure UI shows the new option immediately
    await nextTick();

    // Prepare option data for API
    const newOptionIndex = store.radioOptions.length - 1;
    const newOption = store.radioOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Log the successful option creation in OptionBody component
    const totalDuration = performance.now() - clickTimestamp;
    console.log('🎯 OptionBody: Successfully created option via Add Option button:', {
      createdOptionId: createdOption.id,
      itemBlockId: createdOption.itemBlockId,
      optionText: createdOption.optionText,
      value: createdOption.value,
      sequence: createdOption.sequence,
      imagePath: createdOption.imagePath,
      nextSection: createdOption.nextSection,
      totalDurationFromClick: `${totalDuration.toFixed(2)}ms`,
      buttonClickTime: buttonClickTime,
      completionTime: new Date().toISOString(),
    });

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // CRITICAL: Update the local store with backend data to ensure UI sync
      if (store.radioOptions[newOptionIndex]) {
        store.radioOptions[newOptionIndex].optionText = createdOption.optionText;
        store.radioOptions[newOptionIndex].score = createdOption.value;

        // Force reactivity update
        store.radioOptions = [...store.radioOptions];

        console.log('🔄 Updated frontend store with backend data:', {
          index: newOptionIndex,
          optionText: createdOption.optionText,
          score: createdOption.value,
          storeLength: store.radioOptions.length,
        });
      }

      // Log the state comparison for debugging
      console.log('🔍 State Comparison after option creation:', {
        frontendStoreOptions: store.radioOptions.map((opt, idx) => ({
          index: idx,
          optionText: opt.optionText,
          score: opt.score,
          placeholder: opt.placeholder,
          value: opt.value,
        })),
        backendOptions:
          props.itemBlock.options?.map((opt, idx) => ({
            index: idx,
            id: opt.id,
            optionText: opt.optionText,
            value: opt.value,
            sequence: opt.sequence,
          })) || [],
        newlyCreatedOption: {
          id: createdOption.id,
          sequence: createdOption.sequence,
          addedToIndex: newOptionIndex,
        },
      });

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });
    }
  } catch (error) {
    console.error('❌ Failed to create option:', error);

    // Remove the option from store if API call failed
    if (store.radioOptions.length > 1) {
      store.removeOption(store.radioOptions.length - 1);
    }
  } finally {
    isCreatingOption.value = false;
  }
};

// Handler for adding "other" option
const handleAddOtherOption = async () => {
  if (isCreatingOption.value) return;

  const buttonClickTime = new Date().toISOString();
  const clickTimestamp = performance.now();
  console.log(
    `🖱️ [${buttonClickTime}] Add Other Option button clicked for itemBlockId: ${props.itemBlock.id}`,
  );

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOtherOption();

    // Force reactivity update to ensure UI shows the new option immediately
    await nextTick();

    // Prepare option data for API
    const newOptionIndex = store.radioOptions.length - 1;
    const newOption = store.radioOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create other option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Log the successful "other" option creation in OptionBody component
    const totalDuration = performance.now() - clickTimestamp;
    console.log('🎯 OptionBody: Successfully created "other" option via Add Other Option button:', {
      createdOptionId: createdOption.id,
      itemBlockId: createdOption.itemBlockId,
      optionText: createdOption.optionText,
      value: createdOption.value,
      sequence: createdOption.sequence,
      imagePath: createdOption.imagePath,
      nextSection: createdOption.nextSection,
      totalDurationFromClick: `${totalDuration.toFixed(2)}ms`,
      buttonClickTime: buttonClickTime,
      completionTime: new Date().toISOString(),
    });

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // CRITICAL: Update the local store with backend data to ensure UI sync
      if (store.radioOptions[newOptionIndex]) {
        store.radioOptions[newOptionIndex].optionText = createdOption.optionText;
        store.radioOptions[newOptionIndex].score = createdOption.value;

        // Force reactivity update
        store.radioOptions = [...store.radioOptions];

        console.log('🔄 Updated frontend store with "other" option backend data:', {
          index: newOptionIndex,
          optionText: createdOption.optionText,
          score: createdOption.value,
          storeLength: store.radioOptions.length,
        });
      }

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });
    }
  } catch (error) {
    console.error('❌ Failed to create other option:', error);

    // Remove the option from store if API call failed
    if (store.radioOptions.length > 1) {
      store.removeOption(store.radioOptions.length - 1);
    }
  } finally {
    isCreatingOption.value = false;
  }
};
</script>

<style scoped>
.q-input {
  max-width: 400px;
  width: 400px;
  margin-right: 10px;
}
.draggable-row {
  transition: all 0.3s ease;
}

.draggable-row:hover,
.draggable-row[dragged-index]:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.draggable-row.dragging {
  opacity: 0.5;
}
</style>
