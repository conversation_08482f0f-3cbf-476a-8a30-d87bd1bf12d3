import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Option } from 'src/types/models';

// Define types for option creation and update
export interface CreateOptionData {
  optionText: string;
  itemBlockId: number;
  value?: number;
  imagePath?: string;
  nextSection?: number;
  [key: string]: unknown; // Add index signature for FormData compatibility
}

export interface UpdateOptionData {
  optionText?: string;
  itemBlockId: number;
  value?: number;
  imagePath?: string;
  nextSection?: number;
  [key: string]: unknown; // Add index signature for FormData compatibility
}

export class OptionService {
  private path = '/options';

  // Global tracking for duplicate ID detection
  private static createdOptionIds = new Set<number>();
  private static sequenceTracker = new Map<number, number[]>(); // itemBlockId -> sequence numbers
  private static debugHelpersExposed = false;

  constructor() {
    // Expose debug helpers once
    if (!OptionService.debugHelpersExposed) {
      OptionService.exposeDebugHelpers();
      OptionService.debugHelpersExposed = true;
    }
  }

  /**
   * Create option using POST /options with itemBlockId in request body
   * POST /options
   */
  async createOption(data: CreateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const timestamp = new Date().toISOString();
      console.log(`🚀 [${timestamp}] Creating option with data:`, JSON.stringify(data, null, 2));

      const formData = this.toFormData(data, file);

      // Log the exact timing of the API call
      const apiCallStart = performance.now();
      console.log(
        `⏱️ [${timestamp}] Starting API call to POST /options for itemBlockId: ${data.itemBlockId}`,
      );

      const response = await api.post<Option>(this.path, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      const apiCallEnd = performance.now();
      const apiDuration = apiCallEnd - apiCallStart;

      // Log the complete API response in JSON format for inspection
      console.log(
        `✅ [${new Date().toISOString()}] Option creation API response (took ${apiDuration.toFixed(2)}ms):`,
        JSON.stringify(response.data, null, 2),
      );
      console.log('📊 Generated Option IDs and Details:', {
        optionId: response.data.id,
        itemBlockId: response.data.itemBlockId,
        sequence: response.data.sequence,
        optionText: response.data.optionText,
        value: response.data.value,
        imagePath: response.data.imagePath,
        nextSection: response.data.nextSection,
        apiCallDuration: `${apiDuration.toFixed(2)}ms`,
        timestamp: new Date().toISOString(),
      });

      // Check for potential duplicate sequence numbers and IDs
      if (response.data.sequence && response.data.id) {
        // Check for duplicate option IDs
        if (OptionService.createdOptionIds.has(response.data.id)) {
          console.error(
            `🚨 DUPLICATE OPTION ID DETECTED! ID ${response.data.id} was already created!`,
          );
          console.error('🚨 This indicates a serious backend issue with ID generation!');
        } else {
          OptionService.createdOptionIds.add(response.data.id);
        }

        // Track sequence numbers per itemBlock
        const itemBlockId = data.itemBlockId;
        if (!OptionService.sequenceTracker.has(itemBlockId)) {
          OptionService.sequenceTracker.set(itemBlockId, []);
        }
        const sequences = OptionService.sequenceTracker.get(itemBlockId)!;

        if (sequences.includes(response.data.sequence)) {
          console.error(
            `🚨 DUPLICATE SEQUENCE DETECTED! Sequence ${response.data.sequence} already exists for itemBlockId ${itemBlockId}!`,
          );
          console.error('🚨 Existing sequences for this itemBlock:', sequences);
          console.error('🚨 This may cause the same ID issue!');
        } else {
          sequences.push(response.data.sequence);
        }

        console.log(`🔍 Sequence Analysis for itemBlockId ${itemBlockId}:`, {
          generatedSequence: response.data.sequence,
          optionId: response.data.id,
          allSequencesForThisItemBlock: sequences,
          totalCreatedOptionIds: OptionService.createdOptionIds.size,
          timestamp: new Date().toISOString(),
        });
      }

      Notify.create({
        type: 'positive',
        message: 'สร้างตัวเลือกสำเร็จ',
        position: 'top',
      });
      return response.data;
    } catch (error) {
      console.error('❌ Error creating option:', error);
      console.error('❌ Request data was:', JSON.stringify(data, null, 2));
      Notify.create({
        type: 'negative',
        message: 'สร้างตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Create option failed');
    }
  }

  /**
   * Create option for a specific itemBlock (wrapper method)
   * POST /options
   */
  async createOptionForItemBlock(
    itemBlockId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    // Ensure itemBlockId is included in the request body
    const requestData = {
      ...data,
      itemBlockId: itemBlockId,
    };

    return this.createOption(requestData, file);
  }

  /**
   * Create option for a specific question
   * POST /options/{questionId}
   */
  async createOptionForQuestion(
    questionId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    try {
      // Validate required fields
      if (!questionId) {
        throw new Error('questionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }
      console.log(data);

      const formData = this.toFormData(data, file);
      for (const [key, value] of formData.entries()) {
        console.log(`${key}:`, value);
      }

      const response = await api.post<Option>(`${this.path}/${questionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      Notify.create({
        type: 'positive',
        message: 'สร้างตัวเลือกสำเร็จ',
        position: 'top',
      });
      return response.data;
    } catch (error) {
      console.error('Error creating option for question:', error);
      Notify.create({
        type: 'negative',
        message: 'สร้างตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Create option failed');
    }
  }

  createOptionForQuestionFormData(questionId: number, formData: FormData): Promise<Option> {
    return api.post(`/options/${questionId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Update option by optionId
   * PATCH /options/{optionId}
   */
  async updateOption(optionId: number, data: UpdateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      Notify.create({
        type: 'positive',
        message: 'อัปเดตตัวเลือกสำเร็จ',
        position: 'top',
      });
      return response.data;
    } catch (error) {
      console.error('Error updating option:', error);
      Notify.create({
        type: 'negative',
        message: 'อัปเดตตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Update option failed');
    }
  }

  /**
   * Update option for a specific question
   * PATCH /options/{questionId}/{optionId}
   */
  async updateOptionForQuestion(
    questionId: number,
    optionId: number,
    data: UpdateOptionData,
    file?: File,
  ): Promise<Option> {
    try {
      // Validate required fields
      if (!questionId) {
        throw new Error('questionId is required');
      }
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${questionId}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      Notify.create({
        type: 'positive',
        message: 'อัปเดตตัวเลือกสำเร็จ',
        position: 'top',
      });
      return response.data;
    } catch (error) {
      console.error('Error updating option for question:', error);
      Notify.create({
        type: 'negative',
        message: 'อัปเดตตัวเลือกล้มเหลว',
        position: 'top',
      });
      throw new Error('Update option failed');
    }
  }

  // Keep existing methods for backward compatibility
  async getAllOptions(): Promise<Option[]> {
    try {
      const response = await api.get<Option[]>(this.path);
      return response.data;
    } catch (error) {
      console.error('Error fetching all options:', error);
      throw new Error('Fetch options failed');
    }
  }

  async getOptionById(id: number): Promise<Option> {
    try {
      const response = await api.get<Option>(`${this.path}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching option by ID:', error);
      throw new Error('Fetch option failed');
    }
  }

  async removeOption(id: number): Promise<void> {
    try {
      console.log(`🗑️ Attempting to delete option with ID: ${id}`);

      await api.delete(`${this.path}/${id}`);

      console.log(`✅ Successfully deleted option with ID: ${id}`);
      Notify.create({
        type: 'positive',
        message: 'ลบตัวเลือกสำเร็จ',
        position: 'top',
      });
    } catch (error: any) {
      console.error(`❌ Error removing option ${id}:`, error);

      // Log detailed error information
      if (error.response) {
        console.error('❌ Backend error response:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers,
        });

        // Check for specific error types
        if (error.response.status === 500) {
          console.error('🚨 500 Internal Server Error - Possible causes:');
          console.error('   - Foreign key constraint violation (option used in responses)');
          console.error('   - Database connection issue');
          console.error('   - Backend service error');

          if (error.response.data) {
            console.error(
              '   - Backend error details:',
              JSON.stringify(error.response.data, null, 2),
            );
          }
        }
      } else if (error.request) {
        console.error('❌ Network error - no response received:', error.request);
      } else {
        console.error('❌ Request setup error:', error.message);
      }

      Notify.create({
        type: 'negative',
        message: 'ลบตัวเลือกล้มเหลว - ตรวจสอบ console สำหรับรายละเอียด',
        position: 'top',
      });
      throw new Error('Remove option failed');
    }
  }

  /**
   * Helper method to convert data to FormData for multipart/form-data requests
   */
  private toFormData(data: Record<string, unknown>, file?: File): FormData {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && value !== null && !(value instanceof File)) {
          formData.append(key, JSON.stringify(value));
        } else if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          formData.append(key, String(value));
        } else {
          formData.append(key, JSON.stringify(value));
        }
      }
    });

    if (file) {
      formData.append('file', file);
    }

    return formData;
  }

  /**
   * Validate option data before sending to API
   */
  private validateOptionData(data: CreateOptionData | UpdateOptionData): void {
    if (!data.itemBlockId) {
      throw new Error('itemBlockId is required');
    }

    if (
      'optionText' in data &&
      data.optionText !== undefined &&
      typeof data.optionText !== 'string'
    ) {
      throw new Error('optionText must be a string');
    }

    if (data.value !== undefined && typeof data.value !== 'number') {
      throw new Error('value must be a number');
    }
  }

  /**
   * Create option with validation
   */
  async createOptionWithValidation(
    itemBlockId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    this.validateOptionData(data);
    return this.createOptionForItemBlock(itemBlockId, data, file);
  }

  /**
   * Update option with validation
   */
  async updateOptionWithValidation(
    optionId: number,
    data: UpdateOptionData,
    file?: File,
  ): Promise<Option> {
    this.validateOptionData(data);
    return this.updateOption(optionId, data, file);
  }

  /**
   * Clear tracking data (useful for debugging)
   */
  static clearTrackingData(): void {
    OptionService.createdOptionIds.clear();
    OptionService.sequenceTracker.clear();
    console.log('🧹 Cleared option tracking data');
  }

  /**
   * Get current tracking data for debugging
   */
  static getTrackingData(): { createdIds: number[]; sequenceData: Record<number, number[]> } {
    const sequenceData: Record<number, number[]> = {};
    OptionService.sequenceTracker.forEach((sequences, itemBlockId) => {
      sequenceData[itemBlockId] = sequences;
    });

    return {
      createdIds: Array.from(OptionService.createdOptionIds),
      sequenceData,
    };
  }

  /**
   * Debug helper - expose to global window for console debugging
   */
  static exposeDebugHelpers(): void {
    if (typeof window !== 'undefined') {
      (window as any).optionDebug = {
        getTrackingData: () => OptionService.getTrackingData(),
        clearTrackingData: () => OptionService.clearTrackingData(),
        logTrackingData: () => {
          const data = OptionService.getTrackingData();
          console.log('🔍 Option Tracking Data:', data);
          return data;
        },
      };
      console.log('🛠️ Option debug helpers exposed to window.optionDebug');
    }
  }

  /**
   * Update option without notifications (for auto-save)
   * PATCH /options/{optionId}
   */
  async updateOptionSilent(optionId: number, data: UpdateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      // No notification for silent updates (auto-save)
      return response.data;
    } catch (error) {
      console.error('Error updating option (silent):', error);
      throw new Error('Update option failed');
    }
  }
}
