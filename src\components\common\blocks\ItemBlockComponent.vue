<template>
  <q-card class="q-pa-lg q-ma-md item-block-container" @click="handleCardClick">
    <!-- Header with three-dot menu and save indicator -->
    <div class="item-top-bar">
      <div class="row items-center q-gutter-sm">
        <q-btn flat round color="grey-6" size="sm" class="three-dot-menu" @click.stop="toggleMenu">
          <ThreeDots size="xs" color="grey-6" />
          <q-menu v-model="showMenu" anchor="bottom middle" self="top middle">
            <q-list style="min-width: 150px">
              <q-item clickable v-close-popup @click="onDuplicate">
                <q-item-section avatar>
                  <q-icon name="content_copy" />
                </q-item-section>
                <q-item-section>Duplicate</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="onDelete">
                <q-item-section avatar>
                  <q-icon name="delete" />
                </q-item-section>
                <q-item-section>Delete</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
    </div>

    <!-- โหมดปกติ -->
    <template v-if="!showAnswerSettings">
      <div class="row">
        <div class="col">
          <EditorTool
            :key="`question-${itemBlock.id}-${itemBlock.questions?.[0]?.id || 'new'}`"
            label="พิมพ์คำถาม..."
            :initialValue="currentQuestionText"
            v-model:content="headerText"
            @blur="handleQuestionBlur"
            @update:content="updateQuestionText"
          />
        </div>
        <div class="col-1 q-ml-sm">
          <q-btn flat round icon="image" color="grey" class="bg-transparent" padding="sm" />
        </div>
        <div class="col-auto">
          <q-select
            v-model="selectedBlockBody"
            :options="filteredBlockBodyOptions"
            filled
            dense
            style="min-width: 200px"
            color="accent"
            option-label="label"
            map-options
            @update:model-value="onBlockBodyChange"
          >
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <q-icon :name="scope.opt.icon" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <template v-slot:selected>
              <div class="row items-center">
                <q-icon :name="selectedBlockBody?.icon" class="q-mr-sm" />
                <div>{{ selectedBlockBody?.label }}</div>
              </div>
            </template>
          </q-select>
        </div>
      </div>

      <component
        :is="currentComponent"
        :item-block="itemBlock"
        :type="props.type"
        class="q-mb-md q-pt-md q-ml-md"
        @option:created="handleOptionCreated"
        @option:updated="handleOptionUpdated"
        @option:deleted="handleOptionDeleted"
        @update:question="handleGridQuestionUpdate"
        @update:option="handleGridOptionUpdate"
      />

      <q-separator inset color="#898989" />

      <div class="row items-center justify-between q-mt-md no-wrap">
        <div class="col-auto">
          <div v-if="props.type === 'quiz'" class="row items-center q-gutter-sm">
            <q-btn
              class="text-accent"
              icon="event_available"
              label="เฉลยคำตอบ"
              @click="toggleAnswerSettings"
            />
            <div class="text-caption text-grey-7">({{ number }} point)</div>
          </div>
        </div>
        <div class="col-auto">
          <ItemBlockFooter
            label="จำเป็น"
            :is-required="itemBlock.isRequired ?? false"
            @duplicate="onClickDuplicateItem"
            @delete="onClickDeleteItem"
            @update:is-required="handleIsRequiredToggle"
            @click="$emit('focus-fab')"
          />
        </div>
      </div>
    </template>

    <template v-else>
      <div class="row items-center text-h6 q-mb-md text-black">
        <q-icon name="event_available" class="q-mr-sm" size="md" />
        <div>เลือกคำตอบที่ถูกต้อง</div>
      </div>

      <div class="q-mb-md q-pl-sm">
        <div class="row items-center q-gutter-sm">
          <!-- ข้อความคำถาม -->
          <div class="text-subtitle1 text-weight-medium text-black">
            {{ headerText || 'คำถาม' }}
          </div>

          <q-space />
          <q-input
            v-if="props.type === 'quiz'"
            v-model.number="number"
            type="number"
            filled
            :min="0"
            :max="100"
            step="1"
            style="max-width: 100px"
            dense
          />
        </div>
      </div>

      <div class="q-gutter-sm q-ml-sm q-mb-md">
        <div
          v-for="(choice, index) in itemBlock.options"
          :key="index"
          class="row items-center q-pa-sm rounded-borders cursor-pointer"
          :class="{
            'bg-green-1 text-green-10': selectedAnswer === choice.value,
            'bg-grey-2': selectedAnswer !== choice.value,
          }"
          @click="selectedAnswer = choice.value"
        >
          <q-radio
            v-model="selectedAnswer"
            :val="choice.value"
            size="sm"
            color="green"
            class="q-mr-sm"
          />

          <div class="text-body1">{{ choice.optionText }}</div>

          <!-- ไอคอนถูก -->
          <q-space />
          <q-icon v-if="selectedAnswer === choice.value" name="check" color="green" size="sm" />
        </div>
      </div>
      <!-- ปุ่ม -->
      <div class="row items-center q-mt-md">
        <div class="col-auto">
          <q-btn
            v-if="props.type === 'quiz'"
            class="text-accent"
            icon="event_available"
            label="เพิ่มคำตอบข้อเสนอแนะ"
          />
        </div>
        <q-space />
        <div class="col-auto">
          <q-btn color="accent" flat label="เสร็จสิ้น" @click="showAnswerSettings = false" />
        </div>
      </div>
    </template>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref, provide, watch } from 'vue';
import type { Component } from 'vue';
import type { DropdownItemBlockType, ItemBlock, ItemBlockType, Option } from 'src/types/models';
import ItemBlockFooter from 'src/components/common/blocks/ItemBlockFooter.vue';
import EditorTool from 'src/components/common/EditorTool.vue';
import ThreeDots from 'src/components/common/ThreeDots.vue';
import type { BlockBodyOptionsType } from 'src/types/app';
import OptionBody from './OptionBody.vue';
import TextBody from './TextBody.vue';
import CheckBoxBody from './CheckBoxBody.vue';
import GridBody from './GridBody.vue';
import FileUploadBody from './FileUploadBody.vue';
import { blockBodyOptions } from 'src/data/blocks';
import { extractBlockBodyType } from 'src/utils/block_helper';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';
const number = ref<number>(0);
const selectedAnswer = ref<number | null>(null);
const emit = defineEmits([
  'focus-fab',
  'duplicate',
  'delete',
  'update:question',
  'update:type',
  'update:isRequired',
  'update:option',
]);
const showAnswerSettings = ref(false);

function toggleAnswerSettings() {
  showAnswerSettings.value = !showAnswerSettings.value;
}
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
}>();

// Create a reactive reference to itemBlock for template usage
const itemBlock = computed(() => props.itemBlock);

// Initialize assessment service and global store
const assessmentService = new AssessmentService(props.type || 'evaluate');
const globalStore = useGlobalStore();

// Save state tracking
const isSaving = ref(false);

// Last saved values for comparison
const lastSavedQuestionText = ref<string>('');

// Loading state for type updates
const isUpdatingType = ref<boolean>(false);

const headerText = ref<string>('');

// Computed property to get the current question text from props
const currentQuestionText = computed(() => {
  if (props.itemBlock && props.itemBlock.type !== 'HEADER') {
    const firstQuestion = props.itemBlock.questions?.[0];
    return firstQuestion?.questionText || '';
  } else if (props.itemBlock && props.itemBlock.headerBody) {
    return props.itemBlock.headerBody.title || '';
  }
  return '';
});

// Menu state
const showMenu = ref(false);

// Initialize header text based on the itemBlock prop (if available)
// For non-header blocks, use the first question's text
if (props.itemBlock && props.itemBlock.type !== 'HEADER') {
  // For non-header blocks, get question text from the first question
  const firstQuestion = props.itemBlock.questions?.[0];
  headerText.value = firstQuestion?.questionText || '';
  lastSavedQuestionText.value = firstQuestion?.questionText || '';
} else if (props.itemBlock && props.itemBlock.headerBody) {
  // For header blocks, use headerBody title
  headerText.value = props.itemBlock.headerBody.title || '';
}

// Perform save operation on blur for questions
async function performQuestionSave(questionId: number, content: string) {
  try {
    if (!questionId) return;

    // Only save if content has changed
    if (content.trim() === lastSavedQuestionText.value) {
      return;
    }

    isSaving.value = true;
    globalStore.startSaveOperation('Saving...');

    // Prepare the update payload for question following headerBody pattern
    const updatePayload = {
      questionText: content,
      itemBlockId: props.itemBlock.id,
    };

    // Call the updateQuestion API using /questions/{id} endpoint
    const updatedQuestion = await assessmentService.updateQuestion(questionId, updatePayload);

    if (updatedQuestion) {
      // Update the last saved value
      lastSavedQuestionText.value = content;

      globalStore.completeSaveOperation(true, 'Saved successfully');

      // Emit the update to parent components so they can update their state
      emit('update:question', {
        questionId: questionId,
        questionText: updatedQuestion.questionText,
        itemBlockId: props.itemBlock.id,
        updatedQuestion: updatedQuestion,
      });
    }
  } catch (error) {
    console.error('Question save failed:', error);
    globalStore.completeSaveOperation(false, 'Saved successfully');
  } finally {
    isSaving.value = false;
  }
}

// Perform save operation on blur for options
async function performOptionSave(
  optionId: number,
  field: 'optionText' | 'value',
  value: string | number,
) {
  try {
    if (!optionId) return;

    isSaving.value = true;
    globalStore.startSaveOperation('Saving...');

    // Import OptionService for direct option updates
    const { OptionService } = await import('src/services/asm/optionService');
    const optionService = new OptionService();

    // Prepare the update payload for option
    const updatePayload = {
      [field]: value,
      itemBlockId: props.itemBlock.id,
    };

    // Use the silent update method for save (no notifications)
    await optionService.updateOptionSilent(optionId, updatePayload);

    globalStore.completeSaveOperation(true, 'Saved successfully');
  } catch (error) {
    console.error(`❌ Failed to save option ${optionId} ${field}:`, error);
    globalStore.completeSaveOperation(false, 'Saved successfully');
  } finally {
    isSaving.value = false;
  }
}

// Handler for question blur event
async function handleQuestionBlur() {
  const content = headerText.value;
  if (props.itemBlock.type !== 'HEADER') {
    const firstQuestion = props.itemBlock.questions?.[0];
    if (firstQuestion && firstQuestion.id) {
      await performQuestionSave(firstQuestion.id, content);
    }
  }
}

// Watch for changes in the computed question text to update local state
watch(
  currentQuestionText,
  (newQuestionText) => {
    if (newQuestionText !== headerText.value) {
      headerText.value = newQuestionText;
      lastSavedQuestionText.value = newQuestionText;
    }
  },
  { immediate: true }, // Ensure it runs immediately on mount
);

// Watch for prop changes to update local state (exactly like HeaderBlock pattern)
watch(
  () => props.itemBlock.questions?.[0]?.questionText,
  (newQuestionText) => {
    if (props.itemBlock.type !== 'HEADER' && newQuestionText !== undefined) {
      headerText.value = newQuestionText;
      lastSavedQuestionText.value = newQuestionText;
    }
  },
  { immediate: true }, // Ensure it runs immediately on mount
);

// Watch for entire itemBlock changes to handle data refresh scenarios
watch(
  () => props.itemBlock,
  (newItemBlock, oldItemBlock) => {
    if (newItemBlock && newItemBlock.type !== 'HEADER') {
      const firstQuestion = newItemBlock.questions?.[0];
      if (firstQuestion?.questionText !== undefined) {
        // Only update if the question text actually changed
        const oldQuestionText = oldItemBlock?.questions?.[0]?.questionText;
        if (firstQuestion.questionText !== oldQuestionText) {
          headerText.value = firstQuestion.questionText;
          lastSavedQuestionText.value = firstQuestion.questionText;
        }
      }
    } else if (newItemBlock && newItemBlock.headerBody) {
      // For header blocks, use headerBody title
      if (newItemBlock.headerBody.title !== undefined) {
        headerText.value = newItemBlock.headerBody.title;
      }
    }
  },
  { immediate: true, deep: true }, // Deep watch to catch nested changes
);

// Watch for changes in isRequired property to ensure reactivity
watch(
  () => props.itemBlock.isRequired,
  (newIsRequired, oldIsRequired) => {
    // Normalize undefined values to false for comparison
    const normalizedNew = newIsRequired ?? false;
    const normalizedOld = oldIsRequired ?? false;

    // Only log meaningful changes (not undefined → false transitions)
    if (normalizedNew !== normalizedOld) {
      console.log(
        `🔄 ItemBlock ${props.itemBlock.id} isRequired changed from ${normalizedOld} to ${normalizedNew}`,
      );
      // The reactivity should automatically update the UI since we're using props.itemBlock.isRequired directly
      // This watcher is mainly for debugging and ensuring we catch the changes
    }
  },
  { immediate: true },
);

// Expose save functions for child components to use (blur-based)
const triggerQuestionSave = async (questionId: number, content: string) => {
  await performQuestionSave(questionId, content);
};

const triggerOptionSave = async (
  optionId: number,
  field: 'optionText' | 'value',
  value: string | number,
) => {
  await performOptionSave(optionId, field, value);
};

// Provide save functions to child components via provide/inject
provide('autoSave', {
  triggerQuestionAutoSave: triggerQuestionSave,
  triggerOptionAutoSave: triggerOptionSave,
  isSaving,
});

// Update function for question text content (real-time updates without saving)
function updateQuestionText(content: string) {
  headerText.value = content;
  // No auto-save here - save only on blur
}

// Update ItemBlock type via API
async function updateItemBlockType(newType: string) {
  try {
    if (!props.itemBlock.id) {
      console.error('Cannot update ItemBlock type: Missing block ID');
      return;
    }

    isUpdatingType.value = true;

    // Prepare the update payload with the new type
    const updatePayload = {
      type: newType,
    };

    console.log('🔄 Updating ItemBlock type:', {
      blockId: props.itemBlock.id,
      oldType: props.itemBlock.type,
      newType: newType,
      payload: updatePayload,
    });

    // Call the updateBlock API using the existing method
    const updatedBlock = await assessmentService.updateBlock({
      ...props.itemBlock,
      type: newType as ItemBlockType, // Type assertion for the new type
    });

    if (updatedBlock) {
      console.log('✅ ItemBlock type updated successfully:', {
        blockId: updatedBlock.id,
        newType: updatedBlock.type,
      });

      // Success notification removed

      // Emit the successful update to parent components
      emit('update:question', {
        itemBlockId: props.itemBlock.id,
        updatedBlock: updatedBlock,
        typeChanged: true,
      });
    }
  } catch (error) {
    console.error('❌ ItemBlock type update failed:', error);

    // Error notification removed
  } finally {
    isUpdatingType.value = false;
  }
}

// Handle option created event from child components
function handleOptionCreated(newOption: Option) {
  // Emit to parent component to update the assessment store
  emit('update:option', {
    action: 'created',
    itemBlockId: props.itemBlock.id,
    option: newOption,
  });
}

// Handle option updated event from child components
function handleOptionUpdated(optionId: number, updateData: { index: number; option: Option }) {
  // Emit to parent component to update the assessment store
  emit('update:option', {
    action: 'updated',
    itemBlockId: props.itemBlock.id,
    optionId: optionId,
    updateData: updateData,
  });
}

// Handle option deleted event from child components
function handleOptionDeleted(deleteData: {
  deletedOptionId: number;
  deletedIndex: number;
  itemBlockId: number;
}) {
  console.log('🗑️ ItemBlockComponent: Handling option deletion:', deleteData);

  // Emit to parent component to update the assessment store
  emit('update:option', {
    action: 'deleted',
    itemBlockId: deleteData.itemBlockId,
    optionId: deleteData.deletedOptionId,
    deletedIndex: deleteData.deletedIndex,
  });
}

// Handle GRID question updates from GridBody component
function handleGridQuestionUpdate(updateData: {
  questionId?: number;
  questionText?: string;
  itemBlockId: number;
  updatedQuestion?: object;
  action: 'created' | 'updated' | 'deleted';
}) {
  // Forward to parent component with proper structure
  emit('update:question', {
    questionId: updateData.questionId,
    questionText: updateData.questionText,
    itemBlockId: updateData.itemBlockId,
    updatedQuestion: updateData.updatedQuestion,
    gridAction: updateData.action, // Add grid-specific action
  });
}

// Handle GRID option updates from GridBody component
function handleGridOptionUpdate(updateData: {
  action: 'created' | 'updated' | 'deleted';
  itemBlockId: number;
  option?: Option;
  optionId?: number;
  updateData?: { index: number; option: Option };
}) {
  // Forward to parent component
  emit('update:option', {
    action: updateData.action,
    itemBlockId: updateData.itemBlockId,
    option: updateData.option,
    optionId: updateData.optionId,
    updateData: updateData.updateData,
    isGridOption: true, // Flag to identify GRID options
  });
}

// Filter block body options based on type - exclude GRID for quiz type
const filteredBlockBodyOptions = computed(() => {
  if (props.type === 'quiz') {
    return blockBodyOptions.filter((option) => option.value !== 'GRID');
  }
  return blockBodyOptions;
});

const selectedBlockBody = ref<BlockBodyOptionsType>(
  extractBlockBodyType(props.itemBlock) || filteredBlockBodyOptions.value[0]!,
);

const onBlockBodyChange = async (value: BlockBodyOptionsType) => {
  selectedBlockBody.value = value;

  // Update the ItemBlock type via API
  await updateItemBlockType(value.value);

  // Emit the change to parent component
  emit('update:type', value);
};

// Initialize component when mounted
// onMounted(() => {
//   // If it's a grid type, set up the header question
//   if (props.selectedType === 'grid') {
//     // Set up grid questions
//     setupGridQuestions();

//     // Ensure all column choices have score set to 0
//     store?.gridColumnOptions.forEach((choice) => {
//       if (choice) {
//         choice.score = 0;
//       }
//     });

//     // If there are no row questions yet, add at least one
//     if (store?.gridRowQuestions.length === 0) {
//       store?.addRowQuestion();
//       // Set up grid questions again
//       setupGridQuestions();
//     }

//     // Make sure we have a main question text for the header
//     if (!questionTextModel.value || questionTextModel.value.trim() === '') {
//       // Set a default main question if none exists
//       emit('update:question', 'คำถามหลัก');
//
//     }
//   }
// });

const onClickDuplicateItem = () => {
  // Emit duplicate event to parent component (same as three-dot menu)
  emit('duplicate');
};

const onClickDeleteItem = () => {
  // * implement deletion logic here
  emit('delete');
};

// Handle isRequired toggle - frontend only implementation
const handleIsRequiredToggle = (newValue: boolean) => {
  // Ensure we always work with a proper boolean value
  const booleanValue = Boolean(newValue);

  // Update the itemBlock's isRequired property directly (frontend only)
  itemBlock.value.isRequired = booleanValue;

  // Emit the update to parent components for state management
  emit('update:isRequired', {
    itemBlockId: itemBlock.value.id,
    isRequired: booleanValue,
  });
};

// Menu handler functions
function toggleMenu() {
  showMenu.value = !showMenu.value;
}

function onDuplicate() {
  emit('duplicate');
}

function onDelete() {
  emit('delete');
}

const currentComponent = computed<Component>(() => {
  if (!selectedBlockBody.value) {
    return OptionBody; // Default component if none selected
  }
  type ComponentType = Record<DropdownItemBlockType, Component>;

  const components: ComponentType = {
    RADIO: OptionBody,
    TEXTFIELD: TextBody,
    CHECKBOX: CheckBoxBody,
    GRID: GridBody,
    UPLOAD: FileUploadBody,
  };

  return components[selectedBlockBody.value.value];
});

// Handle card click with debouncing to prevent conflicts
let cardClickTimeout: NodeJS.Timeout | null = null;
function handleCardClick(event: Event) {
  // Clear any existing timeout
  if (cardClickTimeout) {
    clearTimeout(cardClickTimeout);
  }

  // Debounce the focus-fab emission to prevent conflicts with rapid events
  cardClickTimeout = setTimeout(() => {
    // Only emit focus-fab if the click wasn't on a button or interactive element
    const target = event.target as HTMLElement;
    if (!target.closest('button') && !target.closest('.q-btn') && !target.closest('.q-menu')) {
      emit('focus-fab');
    }
  }, 100);
}
</script>

<style scoped>
.q-select {
  box-sizing: border-box;
  width: 255px;
  background: #fffdfd;
  border: 1px solid #b1b1b1;
  border-radius: 10px;
}

.main-question-input {
  transition: all 0.3s ease;
}

.item-block-container {
  position: relative;
}

.item-top-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -16px;
  margin-bottom: 8px;
}

.three-dot-menu {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 4px;
  cursor: grab;
  outline: none;
}

.three-dot-menu:hover {
  background-color: transparent;
}

.three-dot-menu:active {
  cursor: grabbing;
}

.three-dot-menu:focus {
  outline: none;
  box-shadow: none;
}

.question-top-bar {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 8px 8px 0 0;
  margin-bottom: 8px;
}

.save-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(25, 118, 210, 0.1);
  border: 1px solid rgba(25, 118, 210, 0.2);
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 0.75rem;
}

.save-text {
  color: #1976d2;
  font-weight: 500;
}
</style>
